-- Script to check active quests and extract QuestName values
local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

-- Function to recursively explore folders and find QuestName values
local function exploreQuests(questContainer, depth)
    depth = depth or 0
    local indent = string.rep("  ", depth)

    print(indent .. "Exploring: " .. questContainer.Name)

    -- Check if this container has a QuestName value
    local questName = questContainer:FindFirstChild("QuestName")
    if questName then
        print(indent .. "  ✓ QuestName found: " .. tostring(questName.Value))
    end

    -- Recursively explore all child folders
    for _, child in pairs(questContainer:GetChildren()) do
        if child:IsA("Folder") or child:IsA("Configuration") or child:IsA("ModuleScript") then
            exploreQuests(child, depth + 1)
        end
    end
end

-- Main execution
print("=== Quest Explorer ===")
print("Checking LocalPlayer.Quests2...")

if LocalPlayer:FindFirstChild("Quests2") then
    print("Quests2 found! Exploring contents...")
    exploreQuests(LocalPlayer.Quests2)

    -- Also print a summary of all quest names
    print("\n=== Quest Summary ===")
    local function collectQuestNames(container, questList)
        questList = questList or {}

        local questName = container:FindFirstChild("QuestName")
        if questName then
            table.insert(questList, {
                path = container:GetFullName(),
                name = tostring(questName.Value)
            })
        end

        for _, child in pairs(container:GetChildren()) do
            if child:IsA("Folder") or child:IsA("Configuration") or child:IsA("ModuleScript") then
                collectQuestNames(child, questList)
            end
        end

        return questList
    end

    local allQuests = collectQuestNames(LocalPlayer.Quests2)

    if #allQuests > 0 then
        print("Found " .. #allQuests .. " quest(s):")
        for i, quest in ipairs(allQuests) do
            print(i .. ". " .. quest.name .. " (Path: " .. quest.path .. ")")
        end
    else
        print("No QuestName values found in any folders.")
    end

else
    print("❌ Quests2 not found in LocalPlayer!")
    print("Available children in LocalPlayer:")
    for _, child in pairs(LocalPlayer:GetChildren()) do
        print("  - " .. child.Name .. " (" .. child.ClassName .. ")")
    end
end

print("=== End Quest Explorer ===")
